/* PDF-specific styles for quotation documents */
@charset 'UTF-8';

@page {
  margin: 10mm;
  size: A4 portrait;
}

body {
  font-family: 'DejaVu Sans', <PERSON><PERSON>, sans-serif !important;
  font-size: 12px !important;
  line-height: 1.5 !important;
  color: #333 !important;
  background: white !important;
}

/* Force simple block layout for all elements */
* {
  box-sizing: border-box !important;
}

/* Remove unwanted borders from timeline and cards */
.card {
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.card-body {
  border: none !important;
  padding: 10px 0 !important;
}

.card-header {
  border: none !important;
  background: none !important;
  padding: 10px 0 !important;
}

.border-top {
  border-top: none !important;
}

/* Timeline specific border removal */
.timeline-item {
  border: none !important;
  border-left: none !important;
  background: none !important;
  margin-bottom: 15px !important;
  padding: 0 !important;
}

/* Remove any remaining borders and spacing */
.border,
.border-left,
.border-right,
.border-bottom,
.shadow-sm,
.rounded-top,
.rounded {
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Center logo and company info */
.card-header {
  text-align: center !important;
  padding: 20px 0 !important;
  border: none !important;
  background: white !important;
}

.card-header .p-3 {
  padding: 15px 0 !important;
  border: 3px solid #fb5d4336 !important;
  border-radius: 8px !important;
  text-align: center !important;
  margin: 0 auto !important;
  max-width: 100% !important;
}

.card-header img {
  display: block !important;
  margin: 0 auto 20px auto !important;
  text-align: center !important;
  max-width: 200px !important;
  height: auto !important;
}

/* Logo centering specific styles - more aggressive */
.mb-3.d-flex.justify-content-center {
  display: block !important;
  text-align: center !important;
  width: 100% !important;
  margin: 0 auto 20px auto !important;
  padding: 0 !important;
}

.flex-shrink-0 {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
  width: 100% !important;
}

.avatar-initial {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
  width: 100% !important;
  border-radius: 0 !important;
  background: none !important;
  box-shadow: none !important;
}

.avatar-initial img {
  display: block !important;
  margin: 0 auto !important;
  max-width: 200px !important;
  height: auto !important;
  text-align: center !important;
}

/* Force center alignment for logo container */
div[style*='text-align: center'] {
  text-align: center !important;
  margin: 0 auto !important;
  display: block !important;
}

/* ENHANCED logo centering - target exact blade structure */
.mb-3.justify-content-center {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
  width: 100% !important;
}

.mb-3.justify-content-center .flex-shrink-0 {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
  width: 100% !important;
}

.mb-3.justify-content-center .avatar-initial.avatar-shadow-primary.rounded-circle {
  display: block !important;
  text-align: center !important;
  margin: 0 auto !important;
  width: 100% !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* Target the specific logo image with multiple selectors */
.mb-3.justify-content-center img,
.avatar-initial img,
.rounded-circle img,
img.img-fluid {
  display: block !important;
  margin: 0 auto !important;
  text-align: center !important;
  max-width: 200px !important;
  height: auto !important;
}

/* Additional logo centering with broader targeting */
.card-header .mb-3 img,
.card-header .avatar-initial img,
.card-header img {
  display: block !important;
  margin: 0 auto !important;
  text-align: center !important;
}

/* Header company information styling */
.card-header .text-blue {
  color: #003d7a !important;
  font-size: 11px !important;
  line-height: 1.4 !important;
  margin-bottom: 3px !important;
  text-align: center !important;
}

.card-header .small {
  font-size: 11px !important;
  line-height: 1.4 !important;
  margin-bottom: 3px !important;
}

/* Reference details styling - CENTER ALIGNED */
.mt-3.text-start {
  margin-top: 20px !important;
  text-align: center !important;
  border-top: 1px solid #ddd !important;
  padding-top: 15px !important;
}

.mt-3.text-start strong {
  font-size: 12px !important;
  font-weight: bold !important;
  margin-bottom: 5px !important;
  display: block !important;
  text-align: center !important;
}

.mt-3.text-start small {
  font-size: 11px !important;
  margin-bottom: 3px !important;
  display: block !important;
  text-align: center !important;
}

.text-heading.fw-medium {
  font-weight: 600 !important;
  color: #333 !important;
}

/* Force center alignment for reference section */
.position-relative {
  text-align: center !important;
}

.position-relative strong,
.position-relative small {
  text-align: center !important;
  display: block !important;
  margin: 0 auto !important;
}

/* Hide elements not needed in PDF */
.position-fixed,
.btn,
.d-print-none,
script,
.edit-duration,
.navbar,
.sidebar {
  display: none !important;
}

/* Hide specific sections for PDF using simpler selectors */
#accordionIncExc-7 {
  display: none !important;
}

.accordion-item.active {
  display: none !important;
}

/* Hide the last card-body which typically contains invoice */
.card-body.border-top:last-of-type {
  display: none !important;
}

/* Container and layout fixes */
.container-xxl {
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
}

.row {
  margin: 0 !important;
  width: 100% !important;
  overflow: hidden !important;
}

.col-12,
.col-md-4,
.col-md-9,
.col-md-3,
.col {
  width: 100% !important;
  padding: 5px !important;
  display: block !important;
  float: none !important;
  margin: 0 !important;
}

/* Simplified layout - avoid complex grid systems */
.col-md-4 {
  width: 100% !important;
  margin-bottom: 15px !important;
  display: block !important;
}

/* Card styling - maintain exact appearance */
.card {
  border: 1px solid #ddd !important;
  margin-bottom: 15px !important;
  page-break-inside: avoid !important;
  background: white !important;
  border-radius: 8px !important;
}

.card-body {
  padding: 20px !important;
}

.card-header {
  padding: 20px !important;
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #ddd !important;
  text-align: center !important;
}

/* Image handling - preserve exact sizing */
img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
  page-break-inside: avoid !important;
  border: none !important;
}

.img-fluid {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
}

/* Specific image styling for attractions */
.img-thumbnail {
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 4px !important;
  max-width: 200px !important;
  height: auto !important;
}

.square-cover-img {
  object-fit: cover !important;
  width: 100% !important;
  height: auto !important;
  max-height: 150px !important;
}

/* Table styling - optimized for PDF */
.table {
  width: 100% !important;
  border-collapse: collapse !important;
  page-break-inside: avoid !important;
  margin-bottom: 15px !important;
  table-layout: fixed !important;
}

.table-sm {
  font-size: 10px !important;
}

.table th,
.table td {
  border: 1px solid #ddd !important;
  padding: 6px 4px !important;
  page-break-inside: avoid !important;
  vertical-align: top !important;
  word-wrap: break-word !important;
  overflow: hidden !important;
  white-space: normal !important;
  line-height: 1.3 !important;
  max-height: none !important;
}

/* HOTEL TABLE - Specific styling for hotel tables */
.table thead th {
  font-size: 10px !important;
  font-weight: bold !important;
  text-align: center !important;
  background-color: #f8f9fa !important;
  padding: 8px 4px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
}

/* Hotel table cells - allow content to wrap to 2 lines */
.table tbody td {
  font-size: 9px !important;
  line-height: 1.2 !important;
  max-width: 120px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  text-align: center !important;
  vertical-align: middle !important;
  padding: 6px 3px !important;
}

/* Specific column widths for hotel table */
.table thead th:nth-child(1), .table tbody td:nth-child(1) {
  width: 18% !important;
  max-width: 140px !important;
} /* Hotel */
.table thead th:nth-child(2), .table tbody td:nth-child(2) {
  width: 12% !important;
  max-width: 100px !important;
} /* City */
.table thead th:nth-child(3), .table tbody td:nth-child(3) {
  width: 20% !important;
  max-width: 150px !important;
} /* Check-in/out */
.table thead th:nth-child(4), .table tbody td:nth-child(4) {
  width: 8% !important;
  max-width: 60px !important;
} /* Nights */
.table thead th:nth-child(5), .table tbody td:nth-child(5) {
  width: 15% !important;
  max-width: 120px !important;
} /* Room Category */
.table thead th:nth-child(6), .table tbody td:nth-child(6) {
  width: 12% !important;
  max-width: 100px !important;
} /* Hotel Category */
.table thead th:nth-child(7), .table tbody td:nth-child(7) {
  width: 15% !important;
  max-width: 120px !important;
} /* Meal Type */

.table-dark th,
.table-dark td {
  background-color: #2c3e50 !important;
  color: white !important;
  border-color: #34495e !important;
  font-size: 9px !important;
  padding: 6px 4px !important;
}

.bg-white {
  background-color: white !important;
}

/* Hotels table specific styling */
.table thead th {
  background-color: #2c3e50 !important;
  color: white !important;
  font-size: 9px !important;
  padding: 6px 4px !important;
  text-align: center !important;
  font-weight: bold !important;
  border: 1px solid #34495e !important;
}

.table tbody td {
  font-size: 9px !important;
  padding: 6px 4px !important;
  text-align: center !important;
  border: 1px solid #ddd !important;
}

/* Timeline styling - simplified for PDF with image positioning */
.timeline {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.timeline-item {
  margin-bottom: 25px !important;
  page-break-inside: avoid !important;
  border: none !important;
  border-left: none !important;
  padding-left: 30px !important;
  position: relative !important;
  display: flex !important;
  align-items: flex-start !important;
}

/* DAY-SPECIFIC page breaks - keep dot and title together */
/* Target timeline items that contain day headers (Day 1, Day 2, etc.) */
.timeline-item:not(:first-child) {
  page-break-before: always !important;
  break-before: page !important;
}

/* First day should not have page break */
.timeline-item:first-child {
  page-break-before: auto !important;
  break-before: auto !important;
}

/* HOTEL SECTION - Force new page for hotel sections */
/* Target hotel table sections specifically */
.table:has(thead th:first-child:contains("HOTEL")),
.table:has(thead th:contains("CIUDAD")),
.card:has(.table thead th:contains("HOTEL")),
.card:has(.table thead th:contains("CIUDAD")) {
  page-break-before: always !important;
  break-before: page !important;
}

/* Alternative approach - target by table header content */
.table thead th:contains("HOTEL") {
  page-break-before: always !important;
  break-before: page !important;
}

/* Target hotel sections by card header text */
.card-header:has(h4:contains("Hotel")),
.card-header:has(h5:contains("Hotel")),
.card-header:has(h6:contains("Hotel")) {
  page-break-before: always !important;
  break-before: page !important;
}

/* More specific targeting for hotel tables */
.table.mb-3.bg-white.table-sm {
  page-break-before: always !important;
  break-before: page !important;
}

/* Target hotel sections by looking for specific table structure */
.card:has(.table.mb-3.bg-white.table-sm) {
  page-break-before: always !important;
  break-before: page !important;
}

/* Force page break before any table with hotel-related headers */
.table:has(th:first-child:contains("HOTEL")),
.table:has(th:contains("NOCHES")),
.table:has(th:contains("HABITACIÓN")) {
  page-break-before: always !important;
  break-before: page !important;
}

/* CRITICAL: Ensure entire day content stays together - no breaks within */
.timeline-item {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  display: block !important;
}

/* Timeline points (green dots) must stay with their content */
.timeline-item .timeline-point {
  page-break-after: avoid !important;
  break-after: avoid !important;
  page-break-before: avoid !important;
  break-before: avoid !important;
}

/* Timeline event (day content) must stay with dot */
.timeline-item .timeline-event {
  page-break-before: avoid !important;
  break-before: avoid !important;
  page-break-after: avoid !important;
  break-after: avoid !important;
}

.timeline-point {
  position: absolute !important;
  left: 0 !important;
  top: 5px !important;
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  background-color: #28a745 !important;
  border: none !important;
  flex-shrink: 0 !important;
}

.timeline-point-success {
  background-color: #28a745 !important;
  border: none !important;
}

.timeline-event {
  // display: flex !important;
  border: none !important;
  width: 100% !important;
  align-items: flex-start !important;
  gap: 15px !important;
}

.timeline-event-title {
  margin-bottom: 15px !important;
  border: none !important;
}

/* Image positioning in timeline */
.timeline-event img {
  width: 150px !important;
  height: auto !important;
  max-height: 100px !important;
  object-fit: cover !important;
  border-radius: 8px !important;
  flex-shrink: 0 !important;
  margin-right: 15px !important;
}

/* Content positioning in timeline */
.timeline-event .content {
  flex: 1 !important;
  padding-left: 0 !important;
}

/* Day content layout - force proper alignment with images on left */
.row.mb-3,
.flex-wrap.d-flex.align-items-center.mb-50.row,
.flex-wrap.gap-2.mb-4.justify-content-between .row {
  // display: flex !important;
  align-items: flex-start !important;
  gap: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  flex-wrap: nowrap !important;
  flex-direction: row !important;
}

/* Image column - LEFT side (25% width) */
.col-md-3,
.col-md-3.col-12,
.mb-2.col-md-3.col-12 {
  flex: 0 0 25% !important;
  width: 25% !important;
  max-width: 25% !important;
  min-width: 25% !important;
  padding: 0 10px 0 0 !important;
  margin: 0 !important;
  display: block !important;
  order: 1 !important;
  box-sizing: border-box !important;
}

/* Content column - RIGHT side (75% width) */
.col-md-9,
.col-md-9.col-12 {
  flex: 0 0 75% !important;
  width: 50% !important;
  max-width: 50% !important;
  padding: 0 0 0 10px !important;
  margin: 0 !important;
  display: block !important;
  order: 2 !important;
  box-sizing: border-box !important;
}

/* Attraction content specific styling */
.flex-wrap.gap-2.mb-4.justify-content-between {
  margin-bottom: 15px !important;
  padding: 0 !important;
}

.flex-wrap.gap-2.mb-4.justify-content-between .row {
  display: flex !important;
  align-items: flex-start !important;
  gap: 15px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Image styling in content sections - force proper sizing */
.col-md-3 img,
.col-md-3.col-12 img,
.mb-2.col-md-3.col-12 img,
.img-thumbnail,
.square-cover-img {
  width: 170px !important;
  max-width: 170px !important;
  min-width: 170px !important;
  height: auto !important;
  max-height: 120px !important;
  object-fit: cover !important;
  border-radius: 8px !important;
  display: block !important;
  margin: 0 auto !important;
  padding: 4px !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Ensure images are properly contained within their columns */
.col-md-3,
.col-md-3.col-12,
.mb-2.col-md-3.col-12 {
  text-align: center !important;
  vertical-align: top !important;
}

/* Content styling improvements */
.col-md-9 h5,
.col-md-9.col-12 h5 {
  font-size: 14px !important;
  font-weight: bold !important;
  margin-bottom: 8px !important;
  color: #333 !important;
  line-height: 1.3 !important;
}

.col-md-9 h6,
.col-md-9.col-12 h6 {
  font-size: 12px !important;
  font-weight: 600 !important;
  margin-bottom: 6px !important;
  color: #555 !important;
  line-height: 1.3 !important;
}

/* COMPACT TITLES - Make long titles more readable */
/* Airport transfer titles - make them flow better */
.timeline-event .col-md-9 h5,
.timeline-event .col-md-9 h6 {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
  line-height: 1.2 !important;
  margin-bottom: 10px !important;
}

/* Specific styling for airport transfer content */
.timeline-event .col-md-9 p {
  font-size: 11px !important;
  line-height: 1.4 !important;
  text-align: justify !important;
  margin-bottom: 8px !important;
  color: #555 !important;
}

/* Content text styling */
.col-md-9 strong,
.col-md-9.col-12 strong {
  display: block !important;
  margin-bottom: 8px !important;
  font-weight: bold !important;
  font-size: 13px !important;
  color: #333 !important;
  line-height: 1.4 !important;
}

.col-md-9 p,
.col-md-9.col-12 p {
  margin: 0 0 8px 0 !important;
  line-height: 1.5 !important;
  font-size: 11px !important;
  color: #555 !important;
  text-align: justify !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* Typography improvements for better readability */
h1, h2, h3, h4, h5, h6 {
  color: #333 !important;
  font-weight: 600 !important;
  margin-bottom: 10px !important;
  line-height: 1.3 !important;
}

/* Spacing and layout improvements */
.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

/* Final layout adjustments */
.container-fluid {
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure proper text wrapping */
* {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* TIMELINE LAYOUT - Image LEFT, Content RIGHT - HIGHEST PRIORITY */
/* Force all timeline events to use horizontal layout with image on left */
.timeline-event .row,
.timeline-event > .row,
.timeline-event .row.mb-3 {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: flex-start !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 100% !important;
  gap: 0 !important;
}

/* Image column - LEFT side (25% width) - OVERRIDE ALL OTHER RULES */
.timeline-event .row .col-md-3,
.timeline-event .col-md-3,
.timeline-event > .row > .col-md-3,
.timeline-event .row.mb-3 .col-md-3 {
  flex: 0 0 25% !important;
  max-width: 25% !important;
  width: 25% !important;
  min-width: 25% !important;
  padding: 0 10px 0 0 !important;
  margin: 0 !important;
  order: 1 !important;
  box-sizing: border-box !important;
  display: block !important;
}

/* Content column - RIGHT side (75% width) - OVERRIDE ALL OTHER RULES */
.timeline-event .row .col-md-9,
.timeline-event .col-md-9,
.timeline-event > .row > .col-md-9,
.timeline-event .row.mb-3 .col-md-9 {
  flex: 0 0 75% !important;
  max-width: 50% !important;
  width: 50% !important;
  padding: 0 0 0 10px !important;
  margin: 0 !important;
  order: 2 !important;
  box-sizing: border-box !important;
  display: block !important;
}

/* AIRPORT TRANSFER - Compact title styling */
.timeline-event h5,
.timeline-event h6 {
  font-size: 14px !important;
  font-weight: bold !important;
  margin-bottom: 8px !important;
  line-height: 1.2 !important;
  color: #333 !important;
}

/* Make airport transfer titles more compact */
.timeline-event .col-md-9 h5:contains("Aeropuerto"),
.timeline-event .col-md-9 h6:contains("Aeropuerto"),
.timeline-event .col-md-9 h5:contains("Airport"),
.timeline-event .col-md-9 h6:contains("Airport") {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

/* DEFAULT TIMELINE IMAGES - Single image on LEFT side */
/* For regular timeline events (airport, attractions, etc.) */
.timeline-event .col-md-3:not(:has(img + img)) {
  display: block !important;
  text-align: center !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
}

/* Single timeline event image - sized for left column */
.timeline-event .col-md-3:not(:has(img + img)) img {
  width: 100% !important;
  max-width: 200px !important;
  height: 150px !important;
  max-height: 150px !important;
  object-fit: cover !important;
  border-radius: 8px !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  display: block !important;
  margin: 0 auto !important;
}

/* HOTEL IMAGES - Force horizontal layout in ONE LINE */
/* Target sections with multiple images (hotel sections) */
.timeline-event .col-md-3:has(img + img) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  gap: 0 !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  width: 100% !important;
}

/* Multiple hotel images - horizontal layout with small margins */
.timeline-event .col-md-3:has(img + img) img {
  flex: 0 0 auto !important;
  width: 70px !important;
  min-width: 70px !important;
  max-width: 70px !important;
  height: 50px !important;
  max-height: 50px !important;
  object-fit: cover !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  display: block !important;
  margin: 0 6px 0 0 !important;
  flex-shrink: 0 !important;
}

/* Last hotel image - no right margin */
.timeline-event .col-md-3:has(img + img) img:last-child {
  margin-right: 0 !important;
}

/* First hotel image - no left margin */
.timeline-event .col-md-3:has(img + img) img:first-child {
  margin-left: 0 !important;
}

/* FALLBACK - Alternative approach for hotel images if :has() doesn't work */
/* Force all .col-md-3 with multiple images to be horizontal */
.timeline-event .col-md-3 {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  gap: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
}

/* All images in timeline - small horizontal layout */
.timeline-event .col-md-3 img {
  flex: 0 0 auto !important;
  width: 70px !important;
  min-width: 70px !important;
  max-width: 70px !important;
  height: 50px !important;
  max-height: 50px !important;
  object-fit: cover !important;
  border-radius: 4px !important;
  border: 1px solid #ddd !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  display: block !important;
  margin: 0 6px 0 0 !important;
  flex-shrink: 0 !important;
}

/* Single image - make it larger and centered */
.timeline-event .col-md-3 img:only-child {
  width: 150px !important;
  min-width: 150px !important;
  max-width: 150px !important;
  height: 120px !important;
  max-height: 120px !important;
  margin: 0 auto !important;
}

/* Fix broken images - hide empty or broken images */
img[src=""],
img:not([src]),
img[src*="placeholder"],
img[alt=""] {
  display: none !important;
}

/* Ensure images load properly */
img {
  max-width: 100% !important;
  height: auto !important;
  display: block !important;
}

/* CRITICAL FIX - Remove all margins/padding that break the layout */
.timeline-event * {
  box-sizing: border-box !important;
}

/* Force remove Bootstrap margins that break the layout */
.timeline-event .row,
.timeline-event .row.mb-3,
.timeline-event .col-md-3,
.timeline-event .col-md-9 {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* Re-apply only necessary padding */
.timeline-event .col-md-3 {
  padding-right: 10px !important;
}

.timeline-event .col-md-9 {
  padding-left: 10px !important;
}

/* Ensure no Bootstrap grid system interference */
.timeline-event .row::before,
.timeline-event .row::after {
  display: none !important;
}

/* Force exact widths - no flex-grow or flex-shrink */
.timeline-event .col-md-3 {
  flex: 0 0 25% !important;
  width: 50% !important;
  max-width: 50% !important;
  min-width: 50% !important;
  display:inline-block !important;
  //float: left !important;
}

.timeline-event .col-md-9 {
  flex: 0 0 75% !important;
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  display:inline-block !important;
  //float: left !important;

}

/* FINAL OVERRIDE - Ensure timeline-event container allows full width */
.timeline-event {
  width: 100% !important;
  max-width: 100% !important;
  overflow: visible !important;
}

/* Ensure row takes full width of timeline-event */
.timeline-event .row {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100% !important;
  flex: 1 1 100% !important;
}
